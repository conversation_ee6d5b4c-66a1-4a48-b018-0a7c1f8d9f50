// MovistarOttService.ts
export interface MCPSession {
  sessionId: string;
  capabilities: any;
  serverInfo: any;
}

export class MCPClient {
  private baseUrl: string;
  private token: string;
  private session: MCPSession | null = null;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  // Nueva función para parsear respuestas SSE
  private parseSSEResponse(responseText: string): any {
    console.log(`🔍 Parseando respuesta SSE:`, responseText);

    // Buscar la línea que contiene "data: "
    const lines = responseText.split('\n');
    let jsonData = null;

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const dataContent = line.substring(6); // Remover "data: "
        try {
          jsonData = JSON.parse(dataContent);
          break;
        } catch (error) {
          console.error('❌ Error parseando data SSE:', error);
        }
      }
    }

    if (!jsonData) {
      throw new Error('No se encontró data válida en la respuesta SSE');
    }

    return jsonData;
  }

  private async request(method: string, params: any = {}, sessionId?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
      'Accept': 'application/json, text/event-stream',
    };

    const body = {
      jsonrpc: '2.0',
      id: crypto.randomUUID(),
      method,
      params,
    };

    // Añadir session ID si existe y no es la llamada inicial de initialize
    if ((sessionId || this.session?.sessionId) && method !== 'initialize') {
      (body as any).session = sessionId || this.session?.sessionId;
    }

    console.log(`📤 MCP Request: ${method}`, body);

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      const responseText = await response.text();
      console.log(`📥 MCP Response Raw: ${response.status}`, responseText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${responseText}`);
      }

      // Parsear respuesta SSE
      const data = this.parseSSEResponse(responseText);
      console.log(`✅ MCP Response Parsed:`, data);

      if (data.error) {
        throw new Error(`MCP Error ${data.error.code}: ${data.error.message}`);
      }

      return data.result;
    } catch (error) {
      console.error(`❌ HTTP Error:`, error);
      throw error;
    }
  }

  // 1. Inicializar sesión con handshake
  async initialize(): Promise<MCPSession> {
    console.log('🤝 Iniciando handshake MCP...');

    try {
      // Handshake inicial
      const initResult = await this.request('initialize', {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
          prompts: {},
          resources: {},
        },
        clientInfo: {
          name: 'React MCP Client',
          version: '1.0.0',
        },
      });

      console.log('✅ Handshake exitoso:', initResult);

      // Crear sesión - usar sessionId del servidor si existe, sino generar uno
      const sessionId = initResult.sessionId || crypto.randomUUID();
      this.session = {
        sessionId: sessionId,
        capabilities: initResult.capabilities,
        serverInfo: initResult.serverInfo,
      };

      // Confirmar inicialización
      await this.request('initialized', {}, this.session.sessionId);

      return this.session;
    } catch (error) {
      console.error('❌ Error en handshake:', error);
      throw error;
    }
  }

  // 2. Listar herramientas disponibles
  async listTools(): Promise<any[]> {
    if (!this.session) {
      throw new Error('Sesión no inicializada. Llama a initialize() primero.');
    }

    console.log('📋 Listando herramientas MCP...');

    try {
      const result = await this.request('tools/list', {}, this.session.sessionId);
      console.log('🛠️ Herramientas disponibles:', result.tools);
      return result.tools || [];
    } catch (error) {
      console.error('Error listando herramientas:', error);
      throw error;
    }
  }

  // 3. Ejecutar herramienta
  async callTool(name: string, arguments_: any = {}): Promise<any> {
    if (!this.session) {
      throw new Error('Sesión no inicializada. Llama a initialize() primero.');
    }

    console.log(`🔧 Ejecutando herramienta: ${name}`, arguments_);

    try {
      const result = await this.request('tools/call', {
        name,
        arguments: arguments_,
      }, this.session.sessionId);

      console.log(`✅ Resultado de ${name}:`, result);
      return result;
    } catch (error) {
      console.error(`❌ Error ejecutando ${name}:`, error);
      throw error;
    }
  }

  // Cerrar sesión
  async close(): Promise<void> {
    if (this.session) {
      try {
        await this.request('session/close', {}, this.session.sessionId);
      } catch (error) {
        console.warn('Error cerrando sesión:', error);
      }
      this.session = null;
    }
  }
}

// Servicio para Movistar+
export class MovistarOttService {
  private client: MCPClient;
  private tools: any[] = [];

  constructor() {
    const baseUrl = 'https://mcp-server-movistar-plus-1081168799620.europe-west1.run.app/mcp';
    const token = 'c08f853bb7cccf4b9aaca8e4c462f98775da359292ab04f905c01aa702721077';

    this.client = new MCPClient(baseUrl, token);
  }

  async init(): Promise<void> {
    try {
      console.log('🚀 Inicializando cliente MCP...');

      // 1. Handshake
      await this.client.initialize();

      // 2. Cargar herramientas
      this.tools = await this.client.listTools();

      console.log('✅ Cliente MCP inicializado correctamente');
    } catch (error) {
      console.error('❌ Error inicializando cliente MCP:', error);
      throw error;
    }
  }

  // Buscar películas por actor
  async searchByActor(actor: string): Promise<any> {
    return await this.client.callTool('getFilmsByActor', { actor });
  }

  // Buscar películas por título
  async searchByTitle(title: string): Promise<any> {
    return await this.client.callTool('getFilmsByTitle', { title });
  }

  // Buscar películas por género
  async searchByGenre(genre: string): Promise<any> {
    return await this.client.callTool('getFilmsByGenre', { genre });
  }

  // Obtener recomendaciones
  async getRecommendations(): Promise<any> {
    return await this.client.callTool('getFilmsByUserRecommendation', {});
  }

  // Cerrar conexión
  async close(): Promise<void> {
    await this.client.close();
  }

  // Getters
  getAvailableTools(): any[] {
    return this.tools;
  }
}
